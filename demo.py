#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
题库系统演示脚本
不依赖外部库，展示核心功能
"""

import json
import os

def load_questions():
    """加载题库数据"""
    try:
        with open('questions.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载题库失败: {e}")
        return None

def validate_answer(question, user_answer):
    """验证单个答案"""
    question_type = question['type']
    correct_answer = question['answer']
    
    if question_type == 'single_choice':
        # 单选题
        if not isinstance(user_answer, str):
            return False, "单选题答案必须是字符串"
        if user_answer not in question['options']:
            return False, f"选项 {user_answer} 不存在"
        return user_answer == correct_answer, ""
    
    elif question_type == 'multiple_choice':
        # 多选题
        if not isinstance(user_answer, list):
            return False, "多选题答案必须是数组"
        if not user_answer:
            return False, "多选题至少要选择一个选项"
        
        for option in user_answer:
            if option not in question['options']:
                return False, f"选项 {option} 不存在"
        
        # 排序后比较
        user_sorted = sorted(user_answer)
        correct_sorted = sorted(correct_answer)
        return user_sorted == correct_sorted, ""
    
    elif question_type == 'true_false':
        # 判断题
        if isinstance(user_answer, str):
            # 将选项转换为布尔值
            user_bool = user_answer == 'A'
        elif isinstance(user_answer, bool):
            user_bool = user_answer
        else:
            return False, "判断题答案格式错误"
        
        return user_bool == correct_answer, ""
    
    return False, "未知题型"

def demo_validation():
    """演示答案验证功能"""
    print("=" * 60)
    print("长沙公司考试系统 - 答案验证演示")
    print("=" * 60)
    
    # 加载题库
    data = load_questions()
    if not data:
        return
    
    questions = data['questions']
    print(f"题库加载成功，共 {len(questions)} 道题目")
    print()
    
    # 演示不同的答案情况
    test_cases = [
        {
            "name": "完全正确的答案",
            "answers": {
                1: "A",
                2: "B", 
                3: ["A", "B", "C", "E", "F"],
                4: ["A", "B", "C"],
                5: False,
                6: "C"
            }
        },
        {
            "name": "部分错误的答案",
            "answers": {
                1: "B",  # 错误
                2: "B",  # 正确
                3: ["A", "B"],  # 部分正确
                4: ["A", "B", "C"],  # 正确
                5: True,  # 错误
                6: "C"   # 正确
            }
        },
        {
            "name": "格式错误的答案",
            "answers": {
                1: "Z",  # 无效选项
                2: ["A"],  # 格式错误
                3: "A",   # 格式错误
                4: ["A", "B", "C"],  # 正确
                5: "maybe",  # 无效值
                6: "C"    # 正确
            }
        }
    ]
    
    for case in test_cases:
        print(f"测试案例: {case['name']}")
        print("-" * 40)
        
        correct_count = 0
        valid_count = 0
        total_count = len(questions)
        
        for question in questions:
            q_id = question['id']
            user_answer = case['answers'].get(q_id)
            
            if user_answer is None:
                print(f"题目{q_id}: 未答题")
                continue
            
            is_correct, error_msg = validate_answer(question, user_answer)
            
            if error_msg:
                print(f"题目{q_id}: ✗ 格式错误 - {error_msg}")
            else:
                valid_count += 1
                if is_correct:
                    correct_count += 1
                    print(f"题目{q_id}: ✓ 正确")
                else:
                    print(f"题目{q_id}: ✗ 错误")
        
        score = (correct_count / total_count * 100) if total_count > 0 else 0
        print(f"\n结果统计:")
        print(f"  总题数: {total_count}")
        print(f"  有效答案: {valid_count}")
        print(f"  正确答案: {correct_count}")
        print(f"  得分: {score:.1f}分")
        print(f"  正确率: {score:.1f}%")
        print()

def show_question_structure():
    """展示题目结构"""
    print("=" * 60)
    print("JSON数据结构示例")
    print("=" * 60)
    
    data = load_questions()
    if not data:
        return
    
    # 显示考试信息
    exam_info = data.get('exam_info', {})
    print("考试信息:")
    print(f"  标题: {exam_info.get('title', '')}")
    print(f"  总题数: {exam_info.get('total_questions', 0)}")
    print(f"  题型统计: {exam_info.get('type_statistics', {})}")
    print()
    
    # 显示题目示例
    questions = data.get('questions', [])
    type_names = {
        'single_choice': '单选题',
        'multiple_choice': '多选题',
        'true_false': '判断题'
    }
    
    print("题目示例:")
    for i, question in enumerate(questions[:3]):  # 只显示前3题
        print(f"\n题目{question['id']} ({type_names.get(question['type'], question['type'])}):")
        print(f"  题干: {question['question']}")
        print(f"  选项: {question['options']}")
        print(f"  答案: {question['answer']}")
        if question.get('explanation'):
            print(f"  解析: {question['explanation']}")
    
    # 显示验证规则
    validation = data.get('answer_validation', {})
    print(f"\n答案验证规则:")
    for q_type, rule in validation.items():
        type_name = type_names.get(q_type, q_type)
        print(f"  {type_name}:")
        print(f"    数据类型: {rule.get('type', '')}")
        print(f"    有效值: {rule.get('valid_values', [])}")
        print(f"    说明: {rule.get('description', '')}")

def main():
    """主函数"""
    print("长沙公司考试系统演示")
    print("当前目录文件:")
    
    files = [
        "exam_interface.html - 答题界面",
        "exam_logic.js - 前端逻辑", 
        "questions.json - 题库数据",
        "answer_validator.py - 答案验证器",
        "excel_reader.py - Excel读取器"
    ]
    
    for file_desc in files:
        filename = file_desc.split(' - ')[0]
        status = "✓" if os.path.exists(filename) else "✗"
        print(f"  {status} {file_desc}")
    
    print()
    
    # 运行演示
    show_question_structure()
    demo_validation()
    
    print("=" * 60)
    print("演示完成！")
    print()
    print("使用说明:")
    print("1. 打开 exam_interface.html 开始答题")
    print("2. 使用 python excel_reader.py 从Excel导入题库")
    print("3. 使用 python answer_validator.py 进行答案验证")
    print("4. 查看 README.md 了解详细使用方法")

if __name__ == "__main__":
    main()
