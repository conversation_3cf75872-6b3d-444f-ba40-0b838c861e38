const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// MIME类型映射
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.ico': 'image/x-icon'
};

// 创建HTTP服务器
const server = http.createServer((req, res) => {
    // 解析URL
    const parsedUrl = url.parse(req.url);
    let pathname = parsedUrl.pathname;
    
    // 如果访问根路径，重定向到答题界面
    if (pathname === '/') {
        pathname = '/exam_interface.html';
    }
    
    // 构建文件路径
    const filePath = path.join(__dirname, pathname);
    
    // 获取文件扩展名
    const ext = path.extname(filePath);
    const contentType = mimeTypes[ext] || 'text/plain';
    
    // 设置CORS头部
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    // 处理OPTIONS请求
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    // 读取文件
    fs.readFile(filePath, (err, data) => {
        if (err) {
            if (err.code === 'ENOENT') {
                // 文件不存在
                res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end(`
                    <html>
                    <head><title>404 - 文件未找到</title></head>
                    <body>
                        <h1>404 - 文件未找到</h1>
                        <p>请求的文件 ${pathname} 不存在</p>
                        <p><a href="/exam_interface.html">返回答题界面</a></p>
                    </body>
                    </html>
                `);
            } else {
                // 其他错误
                res.writeHead(500, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end(`
                    <html>
                    <head><title>500 - 服务器错误</title></head>
                    <body>
                        <h1>500 - 服务器错误</h1>
                        <p>读取文件时发生错误: ${err.message}</p>
                    </body>
                    </html>
                `);
            }
        } else {
            // 成功读取文件
            res.writeHead(200, { 'Content-Type': contentType + '; charset=utf-8' });
            res.end(data);
        }
    });
});

// 启动服务器
const PORT = process.env.PORT || 8000;
server.listen(PORT, () => {
    console.log('=' .repeat(60));
    console.log('长沙公司考试系统服务器已启动');
    console.log('=' .repeat(60));
    console.log(`服务器地址: http://localhost:${PORT}`);
    console.log(`答题界面: http://localhost:${PORT}/exam_interface.html`);
    console.log(`题库数据: http://localhost:${PORT}/questions.json`);
    console.log('');
    console.log('系统功能:');
    console.log('  ✓ 在线答题界面');
    console.log('  ✓ 单选题、多选题、判断题支持');
    console.log('  ✓ 实时进度跟踪');
    console.log('  ✓ 答案验证和成绩统计');
    console.log('');
    console.log('按 Ctrl+C 停止服务器');
    console.log('=' .repeat(60));
});

// 处理进程退出
process.on('SIGINT', () => {
    console.log('\n服务器已停止');
    process.exit(0);
});

// 错误处理
server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.log(`端口 ${PORT} 被占用，尝试使用端口 ${PORT + 1}`);
        server.listen(PORT + 1);
    } else {
        console.error('服务器错误:', err);
    }
});
