<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>长沙公司考试系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .exam-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .question-card {
            background: white;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .question-number {
            background: #667eea;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-weight: bold;
        }
        
        .question-type {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
        }
        
        .question-text {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 20px;
            color: #333;
        }
        
        .options {
            margin-bottom: 20px;
        }
        
        .option {
            display: flex;
            align-items: center;
            padding: 12px;
            margin-bottom: 8px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .option:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        
        .option.selected {
            border-color: #667eea;
            background-color: #e8eaf6;
        }
        
        .option input {
            margin-right: 10px;
        }
        
        .option-label {
            font-weight: bold;
            margin-right: 8px;
            color: #667eea;
        }
        
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        
        .btn {
            padding: 12px 30px;
            margin: 0 10px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .progress-bar {
            background: #e0e0e0;
            height: 8px;
            border-radius: 4px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .result-card {
            background: white;
            padding: 30px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .score {
            font-size: 48px;
            font-weight: bold;
            color: #667eea;
            margin: 20px 0;
        }
        
        .hidden {
            display: none;
        }
        
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
        }
        
        .question-status {
            display: flex;
            gap: 5px;
            margin-bottom: 20px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #e0e0e0;
        }
        
        .status-dot.answered {
            background: #28a745;
        }
        
        .status-dot.current {
            background: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>长沙公司考试系统</h1>
            <p>请认真答题，祝您考试顺利！</p>
        </div>
        
        <div id="examInfo" class="exam-info">
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill" style="width: 0%"></div>
            </div>
            <div class="question-status" id="questionStatus"></div>
            <p>总题数: <span id="totalQuestions">0</span> | 已答题: <span id="answeredCount">0</span> | 当前题目: <span id="currentQuestionNum">1</span></p>
        </div>
        
        <div id="questionContainer"></div>
        
        <div class="controls">
            <button id="prevBtn" class="btn btn-secondary" onclick="previousQuestion()">上一题</button>
            <button id="nextBtn" class="btn btn-primary" onclick="nextQuestion()">下一题</button>
            <button id="submitBtn" class="btn btn-success hidden" onclick="submitExam()">提交答案</button>
        </div>
        
        <div id="resultContainer" class="result-card hidden">
            <h2>考试结果</h2>
            <div class="score" id="finalScore">0</div>
            <p id="resultDetails"></p>
            <button class="btn btn-primary" onclick="restartExam()">重新考试</button>
        </div>
    </div>

    <script src="exam_logic.js"></script>
</body>
</html>
