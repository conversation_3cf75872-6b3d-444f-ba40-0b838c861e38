<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>长沙公司考试系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .exam-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .question-card {
            background: white;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .question-number {
            background: #667eea;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-weight: bold;
        }
        
        .question-type {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
        }
        
        .question-text {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 20px;
            color: #333;
        }
        
        .options {
            margin-bottom: 20px;
        }
        
        .option {
            display: flex;
            align-items: center;
            padding: 12px;
            margin-bottom: 8px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .option:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        
        .option.selected {
            border-color: #667eea;
            background-color: #e8eaf6;
        }
        
        .option input {
            margin-right: 10px;
        }
        
        .option-label {
            font-weight: bold;
            margin-right: 8px;
            color: #667eea;
        }
        
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        
        .btn {
            padding: 12px 30px;
            margin: 0 10px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .progress-bar {
            background: #e0e0e0;
            height: 8px;
            border-radius: 4px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .result-card {
            background: white;
            padding: 30px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .score {
            font-size: 48px;
            font-weight: bold;
            color: #667eea;
            margin: 20px 0;
        }
        
        .hidden {
            display: none;
        }
        
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
        }
        
        .question-status {
            display: flex;
            gap: 5px;
            margin-bottom: 20px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #e0e0e0;
        }
        
        .status-dot.answered {
            background: #28a745;
        }
        
        .status-dot.current {
            background: #667eea;
        }

        /* 详细结果页面样式 */
        .detailed-results-header {
            background: white;
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .result-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .summary-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }

        .summary-item .number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .summary-item .label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }

        .question-results-list {
            display: grid;
            gap: 15px;
        }

        .question-result-item {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #e0e0e0;
        }

        .question-result-item.correct {
            border-left-color: #28a745;
        }

        .question-result-item.incorrect {
            border-left-color: #dc3545;
        }

        .question-result-item.unanswered {
            border-left-color: #ffc107;
        }

        .question-result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .question-result-number {
            font-weight: bold;
            color: #333;
        }

        .question-result-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: bold;
        }

        .status-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .status-icon.correct {
            background: #28a745;
        }

        .status-icon.incorrect {
            background: #dc3545;
        }

        .status-icon.unanswered {
            background: #ffc107;
        }

        .question-result-text {
            font-size: 16px;
            margin-bottom: 15px;
            color: #333;
        }

        .answer-comparison {
            display: grid;
            gap: 10px;
        }

        .answer-row {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
        }

        .answer-label {
            font-weight: bold;
            min-width: 80px;
        }

        .answer-content {
            flex: 1;
        }

        .user-answer {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }

        .user-answer.correct {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .user-answer.incorrect {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .correct-answer {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .unanswered-text {
            color: #856404;
            font-style: italic;
        }

        .result-actions, .detailed-results-actions {
            text-align: center;
            margin: 30px 0;
        }

        .detailed-results-actions {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>长沙公司考试系统</h1>
            <p>请认真答题，祝您考试顺利！</p>
        </div>
        
        <div id="examInfo" class="exam-info">
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill" style="width: 0%"></div>
            </div>
            <div class="question-status" id="questionStatus"></div>
            <p>总题数: <span id="totalQuestions">0</span> | 已答题: <span id="answeredCount">0</span> | 当前题目: <span id="currentQuestionNum">1</span></p>
        </div>
        
        <div id="questionContainer"></div>
        
        <div class="controls">
            <button id="prevBtn" class="btn btn-secondary" onclick="previousQuestion()">上一题</button>
            <button id="nextBtn" class="btn btn-primary" onclick="nextQuestion()">下一题</button>
            <button id="submitBtn" class="btn btn-success hidden" onclick="submitExam()">提交答案</button>
        </div>
        
        <div id="resultContainer" class="result-card hidden">
            <h2>考试结果</h2>
            <div class="score" id="finalScore">0</div>
            <p id="resultDetails"></p>
            <div class="result-actions">
                <button class="btn btn-primary" onclick="showDetailedResults()">查看详细结果</button>
                <button class="btn btn-secondary" onclick="restartExam()">重新考试</button>
            </div>
        </div>

        <div id="detailedResultsContainer" class="hidden">
            <div class="detailed-results-header">
                <h2>详细答题结果</h2>
                <div class="result-summary" id="detailedSummary"></div>
            </div>
            <div id="questionResultsList" class="question-results-list"></div>
            <div class="detailed-results-actions">
                <button class="btn btn-secondary" onclick="backToSummary()">返回总结</button>
                <button class="btn btn-primary" onclick="restartExam()">重新考试</button>
            </div>
        </div>
    </div>

    <script src="exam_logic.js"></script>
</body>
</html>
