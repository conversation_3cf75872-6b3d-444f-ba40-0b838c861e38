/**
 * 考试系统前端逻辑
 */

class ExamSystem {
    constructor() {
        this.questions = [];
        this.currentQuestionIndex = 0;
        this.userAnswers = {};
        this.examData = null;
        this.init();
    }
    
    async init() {
        try {
            // 加载题库数据
            await this.loadQuestions();
            this.setupExam();
            this.displayCurrentQuestion();
        } catch (error) {
            console.error('初始化考试系统失败:', error);
            alert('加载题库失败，请检查数据文件');
        }
    }
    
    async loadQuestions() {
        try {
            // 尝试加载生成的题库文件
            const response = await fetch('questions.json');
            if (!response.ok) {
                throw new Error('无法加载题库文件');
            }
            this.examData = await response.json();
            this.questions = this.examData.questions;
        } catch (error) {
            // 如果无法加载文件，使用示例数据
            console.warn('无法加载questions.json，使用示例数据');
            this.examData = this.getExampleData();
            this.questions = this.examData.questions;
        }
    }
    
    getExampleData() {
        return {
            "exam_info": {
                "title": "长沙公司考试试题",
                "total_questions": 3
            },
            "questions": [
                {
                    "id": 1,
                    "type": "single_choice",
                    "question": "以下哪个是JavaScript的数据类型？",
                    "options": {
                        "A": "String",
                        "B": "Integer",
                        "C": "Float",
                        "D": "Character"
                    },
                    "answer": "A"
                },
                {
                    "id": 2,
                    "type": "multiple_choice", 
                    "question": "以下哪些是前端开发框架？（多选）",
                    "options": {
                        "A": "React",
                        "B": "Vue",
                        "C": "Angular",
                        "D": "Django"
                    },
                    "answer": ["A", "B", "C"]
                },
                {
                    "id": 3,
                    "type": "true_false",
                    "question": "HTML是一种编程语言。",
                    "options": {
                        "A": "正确",
                        "B": "错误"
                    },
                    "answer": false
                }
            ]
        };
    }
    
    setupExam() {
        // 初始化用户答案
        this.questions.forEach(q => {
            this.userAnswers[q.id] = null;
        });
        
        // 更新考试信息
        document.getElementById('totalQuestions').textContent = this.questions.length;
        this.updateProgress();
        this.createQuestionStatus();
    }
    
    createQuestionStatus() {
        const statusContainer = document.getElementById('questionStatus');
        statusContainer.innerHTML = '';
        
        this.questions.forEach((_, index) => {
            const dot = document.createElement('div');
            dot.className = 'status-dot';
            if (index === this.currentQuestionIndex) {
                dot.classList.add('current');
            }
            statusContainer.appendChild(dot);
        });
    }
    
    displayCurrentQuestion() {
        const question = this.questions[this.currentQuestionIndex];
        const container = document.getElementById('questionContainer');
        
        // 获取题型显示名称
        const typeNames = {
            'single_choice': '单选题',
            'multiple_choice': '多选题',
            'true_false': '判断题'
        };
        
        let optionsHtml = '';
        const inputType = question.type === 'multiple_choice' ? 'checkbox' : 'radio';
        const inputName = `question_${question.id}`;
        
        Object.entries(question.options).forEach(([key, value]) => {
            const isChecked = this.isOptionSelected(question.id, key);
            optionsHtml += `
                <div class="option ${isChecked ? 'selected' : ''}" onclick="selectOption(${question.id}, '${key}', '${question.type}')">
                    <input type="${inputType}" name="${inputName}" value="${key}" ${isChecked ? 'checked' : ''}>
                    <span class="option-label">${key}.</span>
                    <span>${value}</span>
                </div>
            `;
        });
        
        container.innerHTML = `
            <div class="question-card">
                <div class="question-header">
                    <span class="question-number">第 ${this.currentQuestionIndex + 1} 题</span>
                    <span class="question-type">${typeNames[question.type]}</span>
                </div>
                <div class="question-text">${question.question}</div>
                <div class="options">
                    ${optionsHtml}
                </div>
            </div>
        `;
        
        this.updateNavigation();
        this.updateProgress();
        this.updateQuestionStatus();
    }
    
    isOptionSelected(questionId, optionKey) {
        const answer = this.userAnswers[questionId];
        if (!answer) return false;
        
        if (Array.isArray(answer)) {
            return answer.includes(optionKey);
        }
        return answer === optionKey;
    }
    
    updateNavigation() {
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const submitBtn = document.getElementById('submitBtn');
        
        prevBtn.style.display = this.currentQuestionIndex === 0 ? 'none' : 'inline-block';
        
        if (this.currentQuestionIndex === this.questions.length - 1) {
            nextBtn.classList.add('hidden');
            submitBtn.classList.remove('hidden');
        } else {
            nextBtn.classList.remove('hidden');
            submitBtn.classList.add('hidden');
        }
        
        document.getElementById('currentQuestionNum').textContent = this.currentQuestionIndex + 1;
    }
    
    updateProgress() {
        const answeredCount = Object.values(this.userAnswers).filter(answer => answer !== null).length;
        const progress = (answeredCount / this.questions.length) * 100;
        
        document.getElementById('progressFill').style.width = `${progress}%`;
        document.getElementById('answeredCount').textContent = answeredCount;
    }
    
    updateQuestionStatus() {
        const dots = document.querySelectorAll('.status-dot');
        dots.forEach((dot, index) => {
            dot.className = 'status-dot';
            if (index === this.currentQuestionIndex) {
                dot.classList.add('current');
            } else if (this.userAnswers[this.questions[index].id] !== null) {
                dot.classList.add('answered');
            }
        });
    }
}

// 全局变量
let examSystem;

// 选择选项
function selectOption(questionId, optionKey, questionType) {
    const question = examSystem.questions.find(q => q.id === questionId);
    
    if (questionType === 'multiple_choice') {
        // 多选题处理
        if (!examSystem.userAnswers[questionId]) {
            examSystem.userAnswers[questionId] = [];
        }
        
        const answers = examSystem.userAnswers[questionId];
        const index = answers.indexOf(optionKey);
        
        if (index > -1) {
            answers.splice(index, 1);
        } else {
            answers.push(optionKey);
        }
        
        if (answers.length === 0) {
            examSystem.userAnswers[questionId] = null;
        }
    } else {
        // 单选题和判断题处理
        examSystem.userAnswers[questionId] = optionKey;
    }
    
    examSystem.displayCurrentQuestion();
}

// 上一题
function previousQuestion() {
    if (examSystem.currentQuestionIndex > 0) {
        examSystem.currentQuestionIndex--;
        examSystem.displayCurrentQuestion();
    }
}

// 下一题
function nextQuestion() {
    if (examSystem.currentQuestionIndex < examSystem.questions.length - 1) {
        examSystem.currentQuestionIndex++;
        examSystem.displayCurrentQuestion();
    }
}

// 提交考试
function submitExam() {
    const unansweredQuestions = [];
    examSystem.questions.forEach(q => {
        if (examSystem.userAnswers[q.id] === null) {
            unansweredQuestions.push(q.id);
        }
    });
    
    if (unansweredQuestions.length > 0) {
        const confirm = window.confirm(`还有 ${unansweredQuestions.length} 道题未答，确定要提交吗？`);
        if (!confirm) return;
    }
    
    const result = calculateScore();
    displayResult(result);
}

// 计算分数
function calculateScore() {
    let correctCount = 0;
    let totalCount = examSystem.questions.length;
    const details = [];
    
    examSystem.questions.forEach(question => {
        const userAnswer = examSystem.userAnswers[question.id];
        const correctAnswer = question.answer;
        let isCorrect = false;
        
        if (question.type === 'multiple_choice') {
            // 多选题：答案完全匹配才算正确
            if (userAnswer && correctAnswer) {
                const userSorted = [...userAnswer].sort();
                const correctSorted = [...correctAnswer].sort();
                isCorrect = JSON.stringify(userSorted) === JSON.stringify(correctSorted);
            }
        } else if (question.type === 'true_false') {
            // 判断题：将用户选择转换为布尔值
            const userBool = userAnswer === 'A' ? true : userAnswer === 'B' ? false : null;
            isCorrect = userBool === correctAnswer;
        } else {
            // 单选题
            isCorrect = userAnswer === correctAnswer;
        }
        
        if (isCorrect) correctCount++;
        
        details.push({
            questionId: question.id,
            question: question.question,
            userAnswer: userAnswer,
            correctAnswer: correctAnswer,
            isCorrect: isCorrect
        });
    });
    
    return {
        score: Math.round((correctCount / totalCount) * 100),
        correctCount: correctCount,
        totalCount: totalCount,
        details: details
    };
}

// 显示结果
function displayResult(result) {
    document.getElementById('questionContainer').classList.add('hidden');
    document.querySelector('.controls').classList.add('hidden');
    document.getElementById('examInfo').classList.add('hidden');

    const resultContainer = document.getElementById('resultContainer');
    document.getElementById('finalScore').textContent = `${result.score}分`;
    document.getElementById('resultDetails').innerHTML = `
        正确答题: ${result.correctCount}/${result.totalCount}<br>
        正确率: ${result.score}%
    `;

    // 保存结果数据供详细页面使用
    window.examResult = result;

    resultContainer.classList.remove('hidden');
}

// 显示详细结果
function showDetailedResults() {
    document.getElementById('resultContainer').classList.add('hidden');
    document.getElementById('detailedResultsContainer').classList.remove('hidden');

    const result = window.examResult;
    if (!result) return;

    // 显示详细统计
    const summaryContainer = document.getElementById('detailedSummary');
    summaryContainer.innerHTML = `
        <div class="summary-item">
            <div class="number">${result.totalCount}</div>
            <div class="label">总题数</div>
        </div>
        <div class="summary-item">
            <div class="number">${result.correctCount}</div>
            <div class="label">正确题数</div>
        </div>
        <div class="summary-item">
            <div class="number">${result.totalCount - result.correctCount}</div>
            <div class="label">错误题数</div>
        </div>
        <div class="summary-item">
            <div class="number">${result.score}%</div>
            <div class="label">正确率</div>
        </div>
    `;

    // 显示每道题的详细结果
    const listContainer = document.getElementById('questionResultsList');
    listContainer.innerHTML = '';

    result.details.forEach(detail => {
        const question = examSystem.questions.find(q => q.id === detail.questionId);
        if (!question) return;

        const isCorrect = detail.isCorrect;
        const isAnswered = detail.userAnswer !== null && detail.userAnswer !== undefined;

        let statusClass, statusIcon, statusText;
        if (!isAnswered) {
            statusClass = 'unanswered';
            statusIcon = '?';
            statusText = '未答题';
        } else if (isCorrect) {
            statusClass = 'correct';
            statusIcon = '✓';
            statusText = '正确';
        } else {
            statusClass = 'incorrect';
            statusIcon = '✗';
            statusText = '错误';
        }

        // 格式化用户答案和正确答案
        const userAnswerText = formatAnswer(detail.userAnswer, question.type);
        const correctAnswerText = formatAnswer(detail.correctAnswer, question.type);

        const questionItem = document.createElement('div');
        questionItem.className = `question-result-item ${statusClass}`;
        questionItem.innerHTML = `
            <div class="question-result-header">
                <span class="question-result-number">题目 ${detail.questionId}</span>
                <div class="question-result-status">
                    <span class="status-icon ${statusClass}">${statusIcon}</span>
                    <span>${statusText}</span>
                </div>
            </div>
            <div class="question-result-text">${question.question}</div>
            <div class="answer-comparison">
                <div class="answer-row user-answer ${statusClass}">
                    <span class="answer-label">你的答案:</span>
                    <span class="answer-content">${userAnswerText}</span>
                </div>
                <div class="answer-row correct-answer">
                    <span class="answer-label">正确答案:</span>
                    <span class="answer-content">${correctAnswerText}</span>
                </div>
            </div>
        `;

        listContainer.appendChild(questionItem);
    });
}

// 格式化答案显示
function formatAnswer(answer, questionType) {
    if (answer === null || answer === undefined) {
        return '<span class="unanswered-text">未答题</span>';
    }

    if (questionType === 'multiple_choice') {
        if (Array.isArray(answer)) {
            return answer.join(', ');
        }
        return String(answer);
    } else if (questionType === 'true_false') {
        if (typeof answer === 'boolean') {
            return answer ? '正确' : '错误';
        } else if (answer === 'A') {
            return '正确';
        } else if (answer === 'B') {
            return '错误';
        }
        return String(answer);
    } else {
        return String(answer);
    }
}

// 返回总结页面
function backToSummary() {
    document.getElementById('detailedResultsContainer').classList.add('hidden');
    document.getElementById('resultContainer').classList.remove('hidden');
}

// 重新考试
function restartExam() {
    location.reload();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    examSystem = new ExamSystem();
});
