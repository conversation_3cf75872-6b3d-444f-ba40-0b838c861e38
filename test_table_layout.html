<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格布局演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .detailed-results-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .result-summary {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .summary-item {
            text-align: center;
        }
        
        .summary-item .number {
            font-size: 20px;
            font-weight: bold;
            color: #667eea;
        }
        
        .summary-item .label {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }
        
        .results-table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .results-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        
        .results-table th {
            background: #f8f9fa;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
            border-bottom: 2px solid #dee2e6;
            color: #495057;
        }
        
        .results-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: top;
        }
        
        .results-table tr:hover {
            background: #f8f9fa;
        }
        
        .question-number {
            font-weight: bold;
            text-align: center;
        }
        
        .question-text {
            word-wrap: break-word;
        }
        
        .status-cell {
            text-align: center;
            width: 80px;
        }
        
        .status-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: bold;
        }
        
        .status-icon.correct {
            background: #28a745;
        }
        
        .status-icon.incorrect {
            background: #dc3545;
        }
        
        .status-icon.unanswered {
            background: #ffc107;
        }
        
        .answer-cell {
            width: 120px;
        }
        
        .answer-text {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 13px;
            display: inline-block;
            min-width: 100px;
            text-align: center;
        }
        
        .user-answer {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .user-answer.correct {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .user-answer.incorrect {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .correct-answer {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .unanswered-text {
            color: #6c757d;
            font-style: italic;
        }
        
        .demo-button {
            display: inline-block;
            padding: 12px 30px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px;
            transition: background 0.3s;
        }
        
        .demo-button:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-header">
            <h1>表格布局详细结果演示</h1>
            <p>紧凑的表格布局，清晰显示答题结果</p>
        </div>
        
        <div class="detailed-results-header">
            <h2>详细答题结果</h2>
            <div class="result-summary">
                <div class="summary-item">
                    <div class="number">10</div>
                    <div class="label">总题数</div>
                </div>
                <div class="summary-item">
                    <div class="number">7</div>
                    <div class="label">正确题数</div>
                </div>
                <div class="summary-item">
                    <div class="number">2</div>
                    <div class="label">错误题数</div>
                </div>
                <div class="summary-item">
                    <div class="number">70%</div>
                    <div class="label">正确率</div>
                </div>
            </div>
        </div>
        
        <div class="results-table-container">
            <table class="results-table">
                <thead>
                    <tr>
                        <th>题号</th>
                        <th>题目</th>
                        <th>状态</th>
                        <th>你的答案</th>
                        <th>正确答案</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="question-number">1</td>
                        <td class="question-text">发现服务存在安全缺陷、漏洞等风险时，应（）。</td>
                        <td class="status-cell">
                            <span class="status-icon correct">✓</span>
                        </td>
                        <td class="answer-cell">
                            <span class="answer-text user-answer correct">B</span>
                        </td>
                        <td class="answer-cell">
                            <span class="answer-text correct-answer">B</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="question-number">2</td>
                        <td class="question-text">遵守公司帐号与口令管理要求，使用的所有口令长度均应大于等于（）位</td>
                        <td class="status-cell">
                            <span class="status-icon incorrect">✗</span>
                        </td>
                        <td class="answer-cell">
                            <span class="answer-text user-answer incorrect">B</span>
                        </td>
                        <td class="answer-cell">
                            <span class="answer-text correct-answer">A</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="question-number">3</td>
                        <td class="question-text">以下哪些是有效的沟通技巧？（多选）</td>
                        <td class="status-cell">
                            <span class="status-icon correct">✓</span>
                        </td>
                        <td class="answer-cell">
                            <span class="answer-text user-answer correct">A, B, C</span>
                        </td>
                        <td class="answer-cell">
                            <span class="answer-text correct-answer">A, B, C</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="question-number">4</td>
                        <td class="question-text">员工可以在工作时间处理私人事务。</td>
                        <td class="status-cell">
                            <span class="status-icon unanswered">?</span>
                        </td>
                        <td class="answer-cell">
                            <span class="answer-text unanswered-text">未答题</span>
                        </td>
                        <td class="answer-cell">
                            <span class="answer-text correct-answer">错误</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="question-number">5</td>
                        <td class="question-text">公司的年度目标制定应该遵循什么原则？</td>
                        <td class="status-cell">
                            <span class="status-icon correct">✓</span>
                        </td>
                        <td class="answer-cell">
                            <span class="answer-text user-answer correct">C</span>
                        </td>
                        <td class="answer-cell">
                            <span class="answer-text correct-answer">C</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="http://localhost:8000/exam_interface.html" class="demo-button">开始真实答题</a>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h3>🎯 表格布局特点：</h3>
            <ul>
                <li><strong>紧凑布局</strong>：所有信息在一个表格中清晰展示</li>
                <li><strong>状态图标</strong>：✓ 正确（绿色）、✗ 错误（红色）、? 未答（黄色）</li>
                <li><strong>答案对比</strong>：用户答案和正确答案并排显示</li>
                <li><strong>颜色区分</strong>：正确答案绿色背景，错误答案红色背景</li>
                <li><strong>响应式设计</strong>：适配不同屏幕尺寸</li>
                <li><strong>悬停效果</strong>：鼠标悬停时行高亮显示</li>
            </ul>
        </div>
    </div>
</body>
</html>
