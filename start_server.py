#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的HTTP服务器启动脚本
用于运行题库系统
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # 添加CORS头部以支持本地文件访问
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

def start_server(port=8000):
    """启动HTTP服务器"""
    try:
        # 确保在正确的目录中
        os.chdir(Path(__file__).parent)
        
        # 检查必要文件是否存在
        required_files = ['exam_interface.html', 'exam_logic.js', 'questions.json']
        missing_files = []
        
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            print(f"错误：缺少必要文件: {', '.join(missing_files)}")
            return False
        
        # 启动服务器
        with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
            print(f"题库系统服务器已启动")
            print(f"访问地址: http://localhost:{port}/exam_interface.html")
            print(f"按 Ctrl+C 停止服务器")
            
            # 自动打开浏览器
            try:
                webbrowser.open(f'http://localhost:{port}/exam_interface.html')
            except:
                print("无法自动打开浏览器，请手动访问上述地址")
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n服务器已停止")
        return True
    except OSError as e:
        if e.errno == 10048:  # 端口被占用
            print(f"端口 {port} 被占用，尝试使用端口 {port + 1}")
            return start_server(port + 1)
        else:
            print(f"启动服务器失败: {e}")
            return False
    except Exception as e:
        print(f"启动服务器时发生错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("长沙公司考试系统")
    print("=" * 50)
    print()
    
    # 显示系统信息
    print("系统文件检查:")
    files_info = [
        ("exam_interface.html", "答题界面"),
        ("exam_logic.js", "前端逻辑"),
        ("questions.json", "题库数据"),
        ("answer_validator.py", "答案验证器"),
        ("excel_reader.py", "Excel读取器")
    ]
    
    for filename, description in files_info:
        status = "✓" if os.path.exists(filename) else "✗"
        print(f"  {status} {filename} - {description}")
    
    print()
    
    # 启动服务器
    if start_server():
        print("系统运行完成")
    else:
        print("系统启动失败")

if __name__ == "__main__":
    main()
