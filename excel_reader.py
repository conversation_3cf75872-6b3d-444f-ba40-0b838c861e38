#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel题库文件读取器
读取Excel文件并转换为JSON格式的题库数据
"""

import pandas as pd
import json
import re
from typing import Dict, List, Any, Union

class ExcelQuestionReader:
    def __init__(self, excel_path: str):
        """
        初始化Excel读取器
        
        Args:
            excel_path: Excel文件路径
        """
        self.excel_path = excel_path
        self.questions = []
        
    def read_excel(self) -> pd.DataFrame:
        """读取Excel文件"""
        try:
            # 读取Excel文件，假设数据在第一个sheet
            df = pd.read_excel(self.excel_path, sheet_name=0)
            print(f"成功读取Excel文件，共{len(df)}行数据")
            return df
        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            return None
    
    def clean_text(self, text: str) -> str:
        """清理文本内容"""
        if pd.isna(text) or text is None:
            return ""
        return str(text).strip()
    
    def determine_question_type(self, type_text: str) -> str:
        """
        根据题型文本确定题目类型
        
        Args:
            type_text: 题型文本
            
        Returns:
            标准化的题型名称
        """
        type_text = self.clean_text(type_text).lower()
        
        if "单选" in type_text or "单项选择" in type_text:
            return "single_choice"
        elif "多选" in type_text or "多项选择" in type_text:
            return "multiple_choice"
        elif "判断" in type_text or "对错" in type_text or "是非" in type_text:
            return "true_false"
        else:
            # 默认为单选题
            return "single_choice"
    
    def parse_options(self, row: pd.Series) -> Dict[str, str]:
        """
        解析选项内容
        
        Args:
            row: Excel行数据
            
        Returns:
            选项字典
        """
        options = {}
        option_columns = ['选项A', '选项B', '选项C', '选项D', '选项E(勿删)', '选项F(勿删)']
        option_keys = ['A', 'B', 'C', 'D', 'E', 'F']
        
        for i, col in enumerate(option_columns):
            if col in row and not pd.isna(row[col]):
                content = self.clean_text(row[col])
                if content:  # 只添加非空选项
                    options[option_keys[i]] = content
        
        return options
    
    def parse_answer(self, answer_text: str, question_type: str, options: Dict[str, str]) -> Union[str, List[str], bool]:
        """
        解析答案
        
        Args:
            answer_text: 答案文本
            question_type: 题目类型
            options: 选项字典
            
        Returns:
            解析后的答案
        """
        if pd.isna(answer_text) or not answer_text:
            return None
            
        answer_text = self.clean_text(answer_text).upper()
        
        if question_type == "true_false":
            # 判断题处理
            if "正确" in answer_text or "对" in answer_text or "TRUE" in answer_text or "T" in answer_text:
                return True
            elif "错误" in answer_text or "错" in answer_text or "FALSE" in answer_text or "F" in answer_text:
                return False
            # 如果是A/B选项，A通常表示正确，B表示错误
            elif answer_text == "A":
                return True
            elif answer_text == "B":
                return False
        
        elif question_type == "multiple_choice":
            # 多选题处理
            # 提取所有字母选项
            matches = re.findall(r'[A-F]', answer_text)
            valid_answers = [match for match in matches if match in options]
            return sorted(list(set(valid_answers))) if valid_answers else None
        
        else:
            # 单选题处理
            match = re.search(r'[A-F]', answer_text)
            if match and match.group() in options:
                return match.group()
        
        return None
    
    def convert_to_json(self) -> Dict[str, Any]:
        """
        将Excel数据转换为JSON格式
        
        Returns:
            JSON格式的题库数据
        """
        df = self.read_excel()
        if df is None:
            return None
        
        questions = []
        question_id = 1
        
        for index, row in df.iterrows():
            # 检查必填字段
            if pd.isna(row.get('题干')) or pd.isna(row.get('题型')):
                print(f"第{index+2}行数据不完整，跳过")
                continue
            
            question_text = self.clean_text(row['题干'])
            question_type = self.determine_question_type(row['题型'])
            
            if not question_text:
                continue
            
            # 解析选项
            options = self.parse_options(row)
            if not options:
                print(f"第{index+2}行没有有效选项，跳过")
                continue
            
            # 解析答案
            answer = None
            if '答案' in row and not pd.isna(row['答案']):
                answer = self.parse_answer(row['答案'], question_type, options)
            
            question_data = {
                "id": question_id,
                "type": question_type,
                "question": question_text,
                "options": options,
                "answer": answer
            }
            
            # 添加解析字段（如果存在）
            if '解析' in row and not pd.isna(row['解析']):
                question_data["explanation"] = self.clean_text(row['解析'])
            
            questions.append(question_data)
            question_id += 1
        
        # 统计题型数量
        type_counts = {}
        for q in questions:
            q_type = q['type']
            type_counts[q_type] = type_counts.get(q_type, 0) + 1
        
        result = {
            "exam_info": {
                "title": "长沙公司考试试题",
                "description": "从Excel文件导入的题库数据",
                "total_questions": len(questions),
                "question_types": {
                    "single_choice": "单选题",
                    "multiple_choice": "多选题",
                    "true_false": "判断题"
                },
                "type_statistics": type_counts
            },
            "questions": questions,
            "answer_validation": {
                "single_choice": {
                    "type": "string",
                    "valid_values": ["A", "B", "C", "D", "E", "F"],
                    "description": "单选题答案为单个选项字符"
                },
                "multiple_choice": {
                    "type": "array",
                    "item_type": "string",
                    "valid_values": ["A", "B", "C", "D", "E", "F"],
                    "description": "多选题答案为选项字符数组"
                },
                "true_false": {
                    "type": "boolean",
                    "valid_values": [True, False],
                    "description": "判断题答案为布尔值"
                }
            }
        }
        
        return result
    
    def save_to_json(self, output_path: str = "questions.json") -> bool:
        """
        保存为JSON文件
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            是否保存成功
        """
        data = self.convert_to_json()
        if data is None:
            return False
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"题库数据已保存到: {output_path}")
            print(f"共导入{data['exam_info']['total_questions']}道题目")
            print(f"题型统计: {data['exam_info']['type_statistics']}")
            return True
        except Exception as e:
            print(f"保存JSON文件失败: {e}")
            return False

def main():
    """主函数"""
    excel_path = "长沙公司考试试题.xlsx"
    reader = ExcelQuestionReader(excel_path)
    reader.save_to_json("questions.json")

if __name__ == "__main__":
    main()
