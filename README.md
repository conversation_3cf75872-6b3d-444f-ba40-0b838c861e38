# 长沙公司考试系统

一个完整的在线题库系统，支持单选题、多选题和判断题，具备答案验证和成绩统计功能。

## 系统特性

- ✅ **多种题型支持**：单选题、多选题、判断题
- ✅ **Excel数据导入**：支持从Excel文件导入题库数据
- ✅ **实时答题界面**：现代化的Web答题界面
- ✅ **答案验证系统**：完整的答案格式验证和正确性检查
- ✅ **成绩统计分析**：详细的考试结果和分题型统计
- ✅ **进度跟踪**：实时显示答题进度和状态

## 文件结构

```
excel_to_dati/
├── exam_interface.html      # 答题界面（前端）
├── exam_logic.js           # 前端逻辑代码
├── questions.json          # 题库数据文件
├── question_template.json  # JSON模板示例
├── excel_reader.py         # Excel文件读取器
├── answer_validator.py     # 答案验证系统
├── start_server.py         # 服务器启动脚本
├── 长沙公司考试试题.xlsx    # Excel题库文件
└── README.md              # 使用说明
```

## 快速开始

### 1. 启动系统

```bash
python start_server.py
```

系统会自动：
- 启动本地HTTP服务器
- 打开浏览器访问答题界面
- 显示访问地址（通常是 http://localhost:8000/exam_interface.html）

### 2. 从Excel导入题库

如果需要从Excel文件导入新的题库数据：

```bash
# 安装依赖（如果还没安装）
pip install pandas openpyxl

# 运行Excel读取器
python excel_reader.py
```

## Excel文件格式要求

Excel文件应包含以下列（表头）：

| 列名 | 是否必填 | 说明 |
|------|----------|------|
| 题干 | 必填 | 题目内容 |
| 题型 | 必填 | 单选题/多选题/判断题 |
| 选项A | 可选 | 选项A内容 |
| 选项B | 可选 | 选项B内容 |
| 选项C | 可选 | 选项C内容 |
| 选项D | 可选 | 选项D内容 |
| 选项E(勿删) | 可选 | 选项E内容 |
| 选项F(勿删) | 可选 | 选项F内容 |
| 答案 | 可选 | 标准答案 |
| 解析 | 可选 | 答案解析 |

### 答案格式说明

- **单选题**：填写选项字母，如 "A"、"B"、"C" 等
- **多选题**：填写多个选项字母，如 "ABC"、"A,B,C"、"A B C" 等
- **判断题**：填写 "正确"/"错误"、"对"/"错" 或 "A"/"B"

## JSON数据结构

### 题库数据格式

```json
{
  "exam_info": {
    "title": "考试标题",
    "total_questions": 题目总数,
    "question_types": {
      "single_choice": "单选题",
      "multiple_choice": "多选题", 
      "true_false": "判断题"
    }
  },
  "questions": [
    {
      "id": 题目ID,
      "type": "题目类型",
      "question": "题目内容",
      "options": {
        "A": "选项A内容",
        "B": "选项B内容"
      },
      "answer": "标准答案",
      "explanation": "答案解析"
    }
  ]
}
```

### 答案验证规则

- **单选题** (`single_choice`)：
  - 数据类型：`string`
  - 有效值：`["A", "B", "C", "D", "E", "F"]`

- **多选题** (`multiple_choice`)：
  - 数据类型：`array`
  - 元素类型：`string`
  - 有效值：`["A", "B", "C", "D", "E", "F"]`

- **判断题** (`true_false`)：
  - 数据类型：`boolean`
  - 有效值：`[true, false]`

## 答案验证系统

### 使用答案验证器

```python
from answer_validator import AnswerValidator

# 创建验证器
validator = AnswerValidator("questions.json")

# 用户答案示例
user_answers = {
    1: "A",           # 单选题
    2: ["A", "B"],    # 多选题  
    3: True           # 判断题
}

# 验证答案
result = validator.validate_exam_answers(user_answers)

# 生成报告
report = validator.generate_report(result, "exam_report.txt")
print(report)
```

### 验证结果格式

```json
{
  "total_questions": 总题数,
  "answered_questions": 已答题数,
  "correct_answers": 正确题数,
  "score": 得分,
  "accuracy": 正确率,
  "type_statistics": {
    "single_choice": {
      "total": 单选题总数,
      "correct": 单选题正确数,
      "accuracy": 单选题正确率
    }
  },
  "results": [
    {
      "question_id": 题目ID,
      "is_correct": 是否正确,
      "user_answer": 用户答案,
      "correct_answer": 正确答案
    }
  ]
}
```

## 界面功能

### 答题界面特性

- **进度显示**：实时显示答题进度条和状态点
- **题型标识**：清晰标识每道题的类型
- **选项交互**：
  - 单选题：单选按钮
  - 多选题：多选框
  - 判断题：单选按钮（正确/错误）
- **导航控制**：上一题/下一题按钮
- **答题状态**：已答题/未答题状态显示

### 结果展示

- **总体成绩**：分数、正确率统计
- **分题型统计**：各题型的正确率分析
- **详细结果**：每道题的答题情况

## 自定义配置

### 修改题库数据

1. 直接编辑 `questions.json` 文件
2. 或使用 `excel_reader.py` 从Excel导入
3. 重新启动服务器即可生效

### 界面样式定制

编辑 `exam_interface.html` 中的CSS样式：

```css
/* 修改主题色 */
.header {
    background: linear-gradient(135deg, #your-color1, #your-color2);
}

/* 修改按钮样式 */
.btn-primary {
    background: #your-primary-color;
}
```

## 技术栈

- **前端**：HTML5 + CSS3 + JavaScript (ES6+)
- **后端**：Python 3.x
- **数据处理**：pandas + openpyxl
- **服务器**：Python内置HTTP服务器

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 故障排除

### 常见问题

1. **端口被占用**
   - 系统会自动尝试下一个端口
   - 或手动指定端口：`python start_server.py`

2. **Excel读取失败**
   - 检查Excel文件格式是否正确
   - 确保安装了pandas和openpyxl：`pip install pandas openpyxl`

3. **题库数据加载失败**
   - 检查 `questions.json` 文件格式
   - 确保JSON语法正确

4. **浏览器无法访问**
   - 检查防火墙设置
   - 尝试使用 `http://127.0.0.1:8000/exam_interface.html`

## 开发扩展

### 添加新题型

1. 在 `questions.json` 中定义新的题型验证规则
2. 在 `exam_logic.js` 中添加新题型的显示逻辑
3. 在 `answer_validator.py` 中添加验证逻辑

### 数据库集成

可以将JSON文件替换为数据库存储：

```python
# 示例：SQLite集成
import sqlite3

class DatabaseQuestionLoader:
    def __init__(self, db_path):
        self.db_path = db_path
    
    def load_questions(self):
        # 从数据库加载题目
        pass
```

## 许可证

本项目仅供学习和内部使用。

## 联系方式

如有问题或建议，请联系开发团队。
