#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
答案验证系统
用于验证用户提交的答案并计算分数
"""

import json
from typing import Dict, List, Any, Union, Tuple
from datetime import datetime

class AnswerValidator:
    def __init__(self, questions_file: str = "questions.json"):
        """
        初始化答案验证器
        
        Args:
            questions_file: 题库JSON文件路径
        """
        self.questions_file = questions_file
        self.questions_data = self.load_questions()
        self.questions = self.questions_data.get('questions', [])
        self.validation_rules = self.questions_data.get('answer_validation', {})
    
    def load_questions(self) -> Dict[str, Any]:
        """加载题库数据"""
        try:
            with open(self.questions_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"题库文件 {self.questions_file} 不存在")
            return {}
        except json.JSONDecodeError as e:
            print(f"题库文件格式错误: {e}")
            return {}
    
    def validate_answer_format(self, question_id: int, user_answer: Any) -> <PERSON><PERSON>[bool, str]:
        """
        验证答案格式是否正确
        
        Args:
            question_id: 题目ID
            user_answer: 用户答案
            
        Returns:
            (是否有效, 错误信息)
        """
        question = self.get_question_by_id(question_id)
        if not question:
            return False, f"题目ID {question_id} 不存在"
        
        question_type = question['type']
        validation_rule = self.validation_rules.get(question_type, {})
        
        if question_type == 'single_choice':
            # 单选题验证
            if not isinstance(user_answer, str):
                return False, "单选题答案必须是字符串"
            if user_answer not in validation_rule.get('valid_values', []):
                return False, f"单选题答案必须是 {validation_rule.get('valid_values')} 中的一个"
            if user_answer not in question.get('options', {}):
                return False, f"选项 {user_answer} 在此题中不存在"
        
        elif question_type == 'multiple_choice':
            # 多选题验证
            if not isinstance(user_answer, list):
                return False, "多选题答案必须是数组"
            if not user_answer:
                return False, "多选题至少要选择一个选项"
            
            valid_options = validation_rule.get('valid_values', [])
            question_options = question.get('options', {})
            
            for option in user_answer:
                if not isinstance(option, str):
                    return False, "多选题选项必须是字符串"
                if option not in valid_options:
                    return False, f"选项 {option} 不是有效的选项字符"
                if option not in question_options:
                    return False, f"选项 {option} 在此题中不存在"
            
            # 检查重复选项
            if len(user_answer) != len(set(user_answer)):
                return False, "多选题不能包含重复选项"
        
        elif question_type == 'true_false':
            # 判断题验证 - 支持布尔值或选项字符
            if isinstance(user_answer, bool):
                # 布尔值格式
                if user_answer not in validation_rule.get('valid_values', []):
                    return False, "判断题答案必须是 true 或 false"
            elif isinstance(user_answer, str):
                # 选项字符格式
                if user_answer not in question.get('options', {}):
                    return False, f"选项 {user_answer} 在此题中不存在"
            else:
                return False, "判断题答案必须是布尔值或选项字符"
        
        return True, ""
    
    def get_question_by_id(self, question_id: int) -> Dict[str, Any]:
        """根据ID获取题目"""
        for question in self.questions:
            if question.get('id') == question_id:
                return question
        return None
    
    def convert_user_answer(self, question_id: int, user_answer: Any) -> Any:
        """
        将用户答案转换为标准格式用于比较
        
        Args:
            question_id: 题目ID
            user_answer: 用户答案
            
        Returns:
            标准化后的答案
        """
        question = self.get_question_by_id(question_id)
        if not question:
            return user_answer
        
        question_type = question['type']
        
        if question_type == 'true_false':
            # 判断题：将选项字符转换为布尔值
            if isinstance(user_answer, str):
                # 假设A表示正确/True，B表示错误/False
                return user_answer == 'A'
            return user_answer
        
        elif question_type == 'multiple_choice':
            # 多选题：排序以便比较
            if isinstance(user_answer, list):
                return sorted(user_answer)
            return user_answer
        
        return user_answer
    
    def check_single_answer(self, question_id: int, user_answer: Any) -> Dict[str, Any]:
        """
        检查单个题目的答案
        
        Args:
            question_id: 题目ID
            user_answer: 用户答案
            
        Returns:
            检查结果字典
        """
        question = self.get_question_by_id(question_id)
        if not question:
            return {
                'question_id': question_id,
                'is_valid': False,
                'is_correct': False,
                'error': f"题目ID {question_id} 不存在"
            }
        
        # 验证答案格式
        is_valid, error_msg = self.validate_answer_format(question_id, user_answer)
        if not is_valid:
            return {
                'question_id': question_id,
                'question': question.get('question', ''),
                'question_type': question.get('type', ''),
                'is_valid': False,
                'is_correct': False,
                'error': error_msg,
                'user_answer': user_answer,
                'correct_answer': question.get('answer')
            }
        
        # 转换答案格式
        converted_user_answer = self.convert_user_answer(question_id, user_answer)
        correct_answer = question.get('answer')
        
        # 比较答案
        is_correct = converted_user_answer == correct_answer
        
        return {
            'question_id': question_id,
            'question': question.get('question', ''),
            'question_type': question.get('type', ''),
            'is_valid': True,
            'is_correct': is_correct,
            'user_answer': user_answer,
            'converted_user_answer': converted_user_answer,
            'correct_answer': correct_answer,
            'explanation': question.get('explanation', '')
        }
    
    def validate_exam_answers(self, user_answers: Dict[int, Any]) -> Dict[str, Any]:
        """
        验证整套试卷的答案
        
        Args:
            user_answers: 用户答案字典 {question_id: answer}
            
        Returns:
            验证结果
        """
        results = []
        correct_count = 0
        valid_count = 0
        total_questions = len(self.questions)
        
        # 检查每个题目
        for question in self.questions:
            question_id = question['id']
            user_answer = user_answers.get(question_id)
            
            if user_answer is None:
                # 未答题
                result = {
                    'question_id': question_id,
                    'question': question.get('question', ''),
                    'question_type': question.get('type', ''),
                    'is_valid': False,
                    'is_correct': False,
                    'error': '未答题',
                    'user_answer': None,
                    'correct_answer': question.get('answer')
                }
            else:
                result = self.check_single_answer(question_id, user_answer)
                if result['is_valid']:
                    valid_count += 1
                    if result['is_correct']:
                        correct_count += 1
            
            results.append(result)
        
        # 计算分数
        score = (correct_count / total_questions * 100) if total_questions > 0 else 0
        
        # 统计各题型正确率
        type_stats = {}
        for result in results:
            q_type = result['question_type']
            if q_type not in type_stats:
                type_stats[q_type] = {'total': 0, 'correct': 0}
            type_stats[q_type]['total'] += 1
            if result['is_correct']:
                type_stats[q_type]['correct'] += 1
        
        # 计算各题型正确率
        for q_type in type_stats:
            stats = type_stats[q_type]
            stats['accuracy'] = (stats['correct'] / stats['total'] * 100) if stats['total'] > 0 else 0
        
        return {
            'exam_info': self.questions_data.get('exam_info', {}),
            'total_questions': total_questions,
            'answered_questions': valid_count,
            'correct_answers': correct_count,
            'score': round(score, 2),
            'accuracy': round((correct_count / total_questions * 100), 2) if total_questions > 0 else 0,
            'type_statistics': type_stats,
            'results': results,
            'timestamp': datetime.now().isoformat()
        }
    
    def generate_report(self, validation_result: Dict[str, Any], output_file: str = None) -> str:
        """
        生成考试报告
        
        Args:
            validation_result: 验证结果
            output_file: 输出文件路径（可选）
            
        Returns:
            报告内容
        """
        report_lines = []
        report_lines.append("=" * 50)
        report_lines.append("考试结果报告")
        report_lines.append("=" * 50)
        
        # 基本信息
        exam_info = validation_result.get('exam_info', {})
        report_lines.append(f"考试名称: {exam_info.get('title', '未知')}")
        report_lines.append(f"考试时间: {validation_result.get('timestamp', '')}")
        report_lines.append("")
        
        # 总体成绩
        report_lines.append("总体成绩:")
        report_lines.append(f"  总题数: {validation_result['total_questions']}")
        report_lines.append(f"  已答题: {validation_result['answered_questions']}")
        report_lines.append(f"  正确题数: {validation_result['correct_answers']}")
        report_lines.append(f"  得分: {validation_result['score']}分")
        report_lines.append(f"  正确率: {validation_result['accuracy']}%")
        report_lines.append("")
        
        # 各题型统计
        report_lines.append("各题型统计:")
        type_names = {
            'single_choice': '单选题',
            'multiple_choice': '多选题',
            'true_false': '判断题'
        }
        
        for q_type, stats in validation_result['type_statistics'].items():
            type_name = type_names.get(q_type, q_type)
            report_lines.append(f"  {type_name}: {stats['correct']}/{stats['total']} (正确率: {stats['accuracy']:.1f}%)")
        
        report_lines.append("")
        report_lines.append("详细结果:")
        report_lines.append("-" * 50)
        
        # 详细结果
        for result in validation_result['results']:
            status = "✓" if result['is_correct'] else "✗"
            report_lines.append(f"{status} 题目{result['question_id']}: {result['question'][:50]}...")
            
            if not result['is_valid']:
                report_lines.append(f"   错误: {result.get('error', '')}")
            else:
                report_lines.append(f"   用户答案: {result['user_answer']}")
                report_lines.append(f"   正确答案: {result['correct_answer']}")
                if result.get('explanation'):
                    report_lines.append(f"   解析: {result['explanation']}")
            report_lines.append("")
        
        report_content = "\n".join(report_lines)
        
        # 保存到文件
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(report_content)
                print(f"报告已保存到: {output_file}")
            except Exception as e:
                print(f"保存报告失败: {e}")
        
        return report_content

def main():
    """示例用法"""
    # 创建验证器
    validator = AnswerValidator("questions.json")
    
    # 示例用户答案
    user_answers = {
        1: "A",           # 单选题
        2: ["A", "B"],    # 多选题
        3: True           # 判断题
    }
    
    # 验证答案
    result = validator.validate_exam_answers(user_answers)
    
    # 生成报告
    report = validator.generate_report(result, "exam_report.txt")
    print(report)

if __name__ == "__main__":
    main()
