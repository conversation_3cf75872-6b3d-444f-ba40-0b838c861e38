#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查题库答案的脚本
"""

import json

def check_answers():
    """检查题库中的答案"""
    try:
        with open('questions.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        questions = data['questions']
        
        print("=" * 60)
        print("题库答案检查")
        print("=" * 60)
        print(f"总题数: {len(questions)}")
        
        # 统计各题型
        type_counts = {}
        answer_counts = {}
        
        for q in questions:
            q_type = q['type']
            type_counts[q_type] = type_counts.get(q_type, 0) + 1
            
            if q['answer'] is not None:
                answer_counts[q_type] = answer_counts.get(q_type, 0) + 1
        
        print("\n题型统计:")
        type_names = {
            'single_choice': '单选题',
            'multiple_choice': '多选题',
            'true_false': '判断题'
        }
        
        for q_type, count in type_counts.items():
            type_name = type_names.get(q_type, q_type)
            answered = answer_counts.get(q_type, 0)
            print(f"  {type_name}: {count}道 (有答案: {answered}道)")
        
        print("\n各题型示例:")
        
        # 单选题示例
        print("\n单选题示例:")
        single_choice = [q for q in questions if q['type'] == 'single_choice'][:3]
        for q in single_choice:
            print(f"  题目{q['id']}: {q['question'][:40]}...")
            print(f"    选项: {list(q['options'].keys())}")
            print(f"    答案: {q['answer']}")
        
        # 多选题示例
        print("\n多选题示例:")
        multiple_choice = [q for q in questions if q['type'] == 'multiple_choice'][:3]
        for q in multiple_choice:
            print(f"  题目{q['id']}: {q['question'][:40]}...")
            print(f"    选项: {list(q['options'].keys())}")
            print(f"    答案: {q['answer']}")
        
        # 判断题示例
        print("\n判断题示例:")
        true_false = [q for q in questions if q['type'] == 'true_false'][:3]
        for q in true_false:
            print(f"  题目{q['id']}: {q['question'][:40]}...")
            print(f"    选项: {list(q['options'].keys())}")
            print(f"    答案: {q['answer']}")
        
        # 检查答案有效性
        print("\n答案有效性检查:")
        invalid_answers = []
        
        for q in questions:
            if q['answer'] is None:
                continue
                
            if q['type'] == 'single_choice':
                if q['answer'] not in q['options']:
                    invalid_answers.append(f"题目{q['id']}: 答案'{q['answer']}'不在选项中")
            elif q['type'] == 'multiple_choice':
                if isinstance(q['answer'], list):
                    for ans in q['answer']:
                        if ans not in q['options']:
                            invalid_answers.append(f"题目{q['id']}: 答案'{ans}'不在选项中")
                else:
                    invalid_answers.append(f"题目{q['id']}: 多选题答案应为数组格式")
        
        if invalid_answers:
            print("  发现无效答案:")
            for invalid in invalid_answers[:10]:  # 只显示前10个
                print(f"    {invalid}")
        else:
            print("  ✓ 所有答案格式正确")
        
        print("\n" + "=" * 60)
        
    except Exception as e:
        print(f"检查失败: {e}")

if __name__ == "__main__":
    check_answers()
