#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
答案验证系统测试脚本
"""

from answer_validator import AnswerValidator
import json

def test_answer_validator():
    """测试答案验证系统"""
    print("=" * 50)
    print("答案验证系统测试")
    print("=" * 50)
    
    # 创建验证器
    validator = AnswerValidator("questions.json")
    
    if not validator.questions:
        print("错误：无法加载题库数据")
        return
    
    print(f"成功加载题库，共 {len(validator.questions)} 道题目")
    print()
    
    # 测试用例1：完全正确的答案
    print("测试用例1：完全正确的答案")
    correct_answers = {
        1: "A",           # 单选题
        2: "B",           # 单选题
        3: ["A", "B", "C", "E", "F"],  # 多选题
        4: ["A", "B", "C"],  # 多选题
        5: False,         # 判断题
        6: "C"            # 单选题
    }
    
    result1 = validator.validate_exam_answers(correct_answers)
    print(f"得分: {result1['score']}分")
    print(f"正确率: {result1['accuracy']}%")
    print()
    
    # 测试用例2：部分错误的答案
    print("测试用例2：部分错误的答案")
    partial_answers = {
        1: "B",           # 错误答案
        2: "B",           # 正确答案
        3: ["A", "B"],    # 部分正确（多选题）
        4: ["A", "B", "C"], # 正确答案
        5: True,          # 错误答案
        6: "C"            # 正确答案
    }
    
    result2 = validator.validate_exam_answers(partial_answers)
    print(f"得分: {result2['score']}分")
    print(f"正确率: {result2['accuracy']}%")
    print()
    
    # 测试用例3：格式错误的答案
    print("测试用例3：格式错误的答案")
    invalid_answers = {
        1: "Z",           # 无效选项
        2: ["A"],         # 单选题用了数组格式
        3: "A",           # 多选题用了字符串格式
        4: ["A", "B", "C"], # 正确格式
        5: "maybe",       # 判断题用了无效值
        6: "C"            # 正确格式
    }
    
    result3 = validator.validate_exam_answers(invalid_answers)
    print(f"得分: {result3['score']}分")
    print(f"有效答案数: {result3['answered_questions']}/{result3['total_questions']}")
    print()
    
    # 显示详细错误信息
    print("格式错误详情:")
    for result in result3['results']:
        if not result['is_valid']:
            print(f"  题目{result['question_id']}: {result['error']}")
    print()
    
    # 测试用例4：未完成的答案
    print("测试用例4：未完成的答案")
    incomplete_answers = {
        1: "A",
        3: ["A", "B", "C", "E", "F"],
        5: False
        # 缺少题目2, 4, 6的答案
    }
    
    result4 = validator.validate_exam_answers(incomplete_answers)
    print(f"得分: {result4['score']}分")
    print(f"已答题: {result4['answered_questions']}/{result4['total_questions']}")
    print()
    
    # 生成详细报告
    print("生成详细报告...")
    report = validator.generate_report(result2, "test_report.txt")
    print("报告已保存到 test_report.txt")
    print()
    
    # 显示题型统计
    print("各题型统计:")
    for q_type, stats in result2['type_statistics'].items():
        type_names = {
            'single_choice': '单选题',
            'multiple_choice': '多选题',
            'true_false': '判断题'
        }
        type_name = type_names.get(q_type, q_type)
        print(f"  {type_name}: {stats['correct']}/{stats['total']} (正确率: {stats['accuracy']:.1f}%)")

def test_single_question():
    """测试单个题目验证"""
    print("\n" + "=" * 50)
    print("单题验证测试")
    print("=" * 50)
    
    validator = AnswerValidator("questions.json")
    
    # 测试各种答案格式
    test_cases = [
        (1, "A", "单选题正确答案"),
        (1, "B", "单选题错误答案"),
        (1, ["A"], "单选题格式错误"),
        (3, ["A", "B", "C", "E", "F"], "多选题正确答案"),
        (3, ["A", "B"], "多选题部分正确"),
        (3, "A", "多选题格式错误"),
        (5, False, "判断题正确答案"),
        (5, True, "判断题错误答案"),
        (5, "A", "判断题选项格式"),
        (5, "maybe", "判断题无效格式")
    ]
    
    for question_id, answer, description in test_cases:
        result = validator.check_single_answer(question_id, answer)
        status = "✓" if result['is_correct'] else "✗"
        valid = "有效" if result['is_valid'] else "无效"
        print(f"{status} {description}: {valid}")
        if not result['is_valid']:
            print(f"    错误: {result['error']}")

if __name__ == "__main__":
    test_answer_validator()
    test_single_question()
