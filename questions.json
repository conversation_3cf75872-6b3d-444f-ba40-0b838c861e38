{"exam_info": {"title": "长沙公司考试试题", "description": "从Excel文件导入的题库数据", "total_questions": 102, "question_types": {"single_choice": "单选题", "multiple_choice": "多选题", "true_false": "判断题"}, "type_statistics": {"single_choice": 22, "multiple_choice": 44, "true_false": 36}}, "questions": [{"id": 1, "type": "single_choice", "question": "发现服务存在安全缺陷、漏洞等风险时，应（）。", "options": {"A": "继续功能开发，有时间再整改。", "B": "立即采取整改加固措施。", "C": "除非公司要求，否则置之不理。", "D": "无需整改"}, "answer": null}, {"id": 2, "type": "single_choice", "question": "遵守公司帐号与口令管理要求，使用的所有口令长度均应大于等于（）位", "options": {"A": "8", "B": "9", "C": "10", "D": "6"}, "answer": null}, {"id": 3, "type": "single_choice", "question": "禁止在互联网大区传输和存储公司商密数据，在互联网大区传输重要数据时应（）", "options": {"A": "明文传输", "B": "加密保护", "C": "压缩存储", "D": "匿名处理", "E": "B"}, "answer": null}, {"id": 4, "type": "single_choice", "question": "不得使用默认端口，原则上数据库端口加（），远程管理端口加10000。", "options": {"A": "10000", "B": "1000", "C": "2000", "D": "20000"}, "answer": null}, {"id": 5, "type": "single_choice", "question": "对于用户登录、密码修改、敏感数据查询等操作，应使用（）方法，并将参数体加密后传输", "options": {"A": "GET", "B": "POST", "C": "PUT", "D": "OPTIONS"}, "answer": null}, {"id": 6, "type": "single_choice", "question": "下列哪项是合规的加密方式", "options": {"A": "base64", "B": "固定密钥的AES", "C": "随机密钥的AES，密钥明文传输", "D": "随机密钥的AES，密钥RSA加密传输"}, "answer": null}, {"id": 7, "type": "single_choice", "question": "下面哪项更符合权限管理要求", "options": {"A": "只检测用户是否登录", "B": "使用全局过滤器检测用户权限，禁止用户访问权限外的接口", "C": "只展示用户权限内的功能点", "D": "统一登录后无需额外鉴权"}, "answer": null}, {"id": 8, "type": "single_choice", "question": "下列不属于jboss安全要求的是", "options": {"A": "JBoss4.2必须需要安装jdk5", "B": "不得使用Jboss的默认配置、端口、管理员账户和密码，必须更改。", "C": "必须使用Jboss 7以上版本。", "D": "对于Jboss管理的用户，必须实施强密码策略，包括长度、复杂度等要求"}, "answer": null}, {"id": 9, "type": "single_choice", "question": "下列不符合jetty安全要求的是", "options": {"A": "修改Jetty的默认配置、端口", "B": "使用默认管理员账户和密码", "C": "只为Jetty用户和应用程序分配必要的访问权限", "D": "及时更改管理员账户和密码"}, "answer": null}, {"id": 10, "type": "single_choice", "question": "下列不符合druid安全要求的是", "options": {"A": "禁止在Druid配置中明文存储敏感信息（如数据库密码、密钥等）", "B": "可以使用druid默认密码", "C": "禁止在Druid开发过程中泄露或共享与数据库相关的敏感信息（如连接字符串、查询语句等）。", "D": "禁用Druid的远程登录功能，只允许本地访问监控页面。或者使用IP白名单限制访问监控页面的IP地址"}, "answer": null}, {"id": 11, "type": "single_choice", "question": "下列不符合nacos安全要求的是", "options": {"A": "开启Nacos集群鉴权能力并正确配置Nacos的访问控制功能，防止未经授权的访问", "B": "禁止使用默认密钥，应设置 NACOS_AUTH_SYSTEM_KEY 变量指定秘钥。", "C": "必须为Nacos配置合理的访问权限和角色管理，确保只有授权的用户才能访问和修改配置数据", "D": "使用nacos默认密码"}, "answer": null}, {"id": 12, "type": "single_choice", "question": "下列对文件下载安全要求描述不正确的是", "options": {"A": "使用fileid而不是文件路径进行文件下载，可以预防任意文件下载漏洞", "B": "限制下载文件的扩展名可以预防任意文件下载漏洞", "C": "..\\不会产生路径穿越问题", "D": "限制用户可下载的文件路径，可以预防任意文件下载漏洞"}, "answer": null}, {"id": 13, "type": "single_choice", "question": "常见的信息泄漏不包括", "options": {"A": "对webpack打包后产生的.map文件直接存储在web目录下", "B": "生产环境开启调试模式", "C": "对数据进行脱敏传输", "D": "密钥明文存储在JS代码中"}, "answer": null}, {"id": 14, "type": "single_choice", "question": "部署系统架构时，需穿透互联网大区、信息内网的项目，应遵守（）的方式", "options": {"A": "数据库部署在互联网大区，应用部署在信息内网", "B": "应用部署在互联网大区，数据库部署在信息内网", "C": "应用、数据库都部署在互联网大区", "D": "应用、数据库都部署在信息内网"}, "answer": null}, {"id": 15, "type": "single_choice", "question": "进行sql查询，单次查询数量，需限制sql语句返回的记录条数要求", "options": {"A": "小于50条", "B": "小于70条", "C": "大于50条", "D": "大于70条"}, "answer": null}, {"id": 16, "type": "single_choice", "question": "关于数据存储安全要求", "options": {"A": "互联网大区可以存储公司重要数据，不得留存3个月以上的普通数据。", "B": "互联网大区不得存储公司重要数据，可以留存3个月以上的普通数据。", "C": "互联网大区不得存储公司重要数据，且不得留存3个月以上的普通数据。", "D": "互联网大区可以存储公司重要数据，且可以留存3个月以上的普通数据。"}, "answer": null}, {"id": 17, "type": "single_choice", "question": "关于参数和数据传输错误的是", "options": {"A": "数据传输不允许数据明文传输，需加密传输", "B": "敏感数据查询操作可以使用POST方法，将参数体加密后传输。", "C": "对于密码修改操作可以使用POST方式，将参数体加密后传输。", "D": "对于用户登录操作，可以使用GET方法"}, "answer": null}, {"id": 18, "type": "single_choice", "question": "对于用户输入进行效验说法正确的是", "options": {"A": "可以只对大小写转换设置过滤器进行效验", "B": "可以只对特殊字符设置过滤器进行效验", "C": "应统一采用大小写转换、特殊字符过滤等过滤器对输入进行效验", "D": "可以不进行效验"}, "answer": null}, {"id": 19, "type": "single_choice", "question": "关于Tomcat说法错误的是", "options": {"A": "必须实施身份验证和授权机制，为Tomcat的管理界面设置复杂且唯一的密码。", "B": "禁止显示详细的错误信息和堆栈跟踪给最终用户。", "C": "配置自定义的错误页面和异常处理机制，以提供友好的错误提示和日志记录。", "D": "可以使用HTTP方法，可以不设置请求头限制。"}, "answer": null}, {"id": 20, "type": "single_choice", "question": "关于Nginx说法错误的是", "options": {"A": "在Nginx配置文件中使用哈希存储方式存储敏感信息", "B": "禁止开启autoindex off目录列表功能", "C": "禁止Nginx配置中的root、alias指令指向的目录不包含../上级目录引用", "D": "在Nginx配置中直接引用外部资源如远程文件、数据库"}, "answer": null}, {"id": 21, "type": "single_choice", "question": "关于Winserver说法错误的是", "options": {"A": "禁止使用弱密码或默认密码作为Windows Server的管理员账户或其他用户账户的密码。", "B": "Windows Server配置强密码策略，并强制用户定期更改密码。", "C": "启用Windows防火墙，只需配置适当的入站规则，限制不必要的网络访问。", "D": "必须通过杀毒软件，为Windows Server安装并启用最新的安全更新和补丁，以修复已知的安全漏洞。"}, "answer": null}, {"id": 22, "type": "single_choice", "question": "关于Linux说法错误的是", "options": {"A": "配置日志记录和分析工具以便监控和分析系统活动。", "B": "启用firewall防火墙，只需配置适当的入站规则，限制不必要的网络访问", "C": "使用强密码策略，并定期更改系统管理员和其他用户的密码。", "D": "使用SSH密钥认证代替密码认证，以提高远程访问的安全性。"}, "answer": null}, {"id": 23, "type": "multiple_choice", "question": "擅自在各类文库、论坛、社交平台、网站、非法链接等互联网上传播公司（）等敏感数据。", "options": {"A": "内部材料", "B": "系统代码", "C": "客户电力信息", "D": "业务数据", "E": "涉密信息", "F": "第三方批量接入数据"}, "answer": null}, {"id": 24, "type": "multiple_choice", "question": "严禁私自将办公终端、外设终端等在（）间跨区交叉使用。", "options": {"A": "管理信息大区网络和互联网大区网络", "B": "信息内网和管理信息大区", "C": "信息内网和信息外网", "D": "互联网大区和信息外网"}, "answer": null}, {"id": 25, "type": "multiple_choice", "question": "严禁擅自打开（）提供的附件或网址。", "options": {"A": "可疑邮件", "B": "垃圾邮件", "C": "不明来源邮件", "D": "跟发件人确认后的邮件"}, "answer": null}, {"id": 26, "type": "multiple_choice", "question": "严禁违反（）实施收集和使用个人信息的行为。", "options": {"A": "法律规定", "B": "行政法规规定", "C": "双方协议约定", "D": "行业标准"}, "answer": null}, {"id": 27, "type": "multiple_choice", "question": "提供的技术服务应符合相关国家标准的强制性要求，不得设置（）。", "options": {"A": "恶意程序", "B": "后门", "C": "合法程序", "D": "waf"}, "answer": null}, {"id": 28, "type": "multiple_choice", "question": "禁止私自将涉及公司的开发文档、代码、各类网站、APP等（）在第三方云网络或平台", "options": {"A": "存储", "B": "部署", "C": "托管"}, "answer": null}, {"id": 29, "type": "multiple_choice", "question": "遵守公司帐号与口令管理要求，使用的所有口令应包含（）。", "options": {"A": "字母", "B": "数字", "C": "特殊字符", "D": "姓名缩写"}, "answer": null}, {"id": 30, "type": "multiple_choice", "question": "本人发生网络安全违规行为时，应（）。", "options": {"A": "及时向上级汇报", "B": "不瞒报", "C": "不谎报", "D": "防止安全风险扩大"}, "answer": null}, {"id": 31, "type": "multiple_choice", "question": "重要信息系统和基础信息网络要与安全防护设施（）。", "options": {"A": "同步规划", "B": "同步建设", "C": "同步运行"}, "answer": null}, {"id": 32, "type": "multiple_choice", "question": "未经检测和许可，不得将任何电子信息、通讯设备（如（））接入电力公司信息网络。", "options": {"A": "计算机", "B": "移动硬盘", "C": "传真机", "D": "复印机"}, "answer": null}, {"id": 33, "type": "multiple_choice", "question": "未经（）批准，严禁擅自将业务系统、设备接入公司网络。", "options": {"A": "科数部", "B": "信通公司", "C": "业务需求部门", "D": "财务部"}, "answer": null}, {"id": 34, "type": "multiple_choice", "question": "SQL查询语句构建，应采用（）或（）的sql语句构建，不得使用字符拼接的方式构建SQL语句，以防止SQL注入。", "options": {"A": "预编译", "B": "参数化", "C": "直接字符拼接", "D": "明文存储数据库密码"}, "answer": null}, {"id": 35, "type": "multiple_choice", "question": "下列选项属于敏感信息的是（）", "options": {"A": "用户名", "B": "用户编号", "C": "身份证号", "D": "手机号", "E": "机构号", "F": "地址"}, "answer": null}, {"id": 36, "type": "multiple_choice", "question": "对于用户输入，统一采用过滤器（如大小写转换、特殊字符过滤等）对输入进行效验，防止非法字符。可以有效预防哪些漏洞", "options": {"A": "sql注入", "B": "越权", "C": "信息泄漏", "D": "存储型XSS（跨站脚本攻击）"}, "answer": null}, {"id": 37, "type": "multiple_choice", "question": "下列哪些做法可以防止文件上传漏洞", "options": {"A": "限制上传文件的扩展名", "B": "使用随机文件名", "C": "不进行任何限制", "D": "如果有文件路径相关参数也需要进行限制"}, "answer": null}, {"id": 38, "type": "multiple_choice", "question": "下列属于信息泄漏防治要求的是", "options": {"A": "禁止在代码中直接硬编码明文敏感信息，如数据库密码、API密钥等。", "B": "严禁把密钥、加解密函数体保存在前端页面。", "C": "不得明文传输用户名、口令等敏感数据。", "D": "禁止把源代码备份打包保存在部署目录下。"}, "answer": null}, {"id": 39, "type": "multiple_choice", "question": "下列能预防或缓解sql注入的方法有（）", "options": {"A": "对于int参数，在代码中进行强制转换", "B": "使用预编译", "C": "检测用户输入是否符合预期", "D": "屏蔽报错信息"}, "answer": null}, {"id": 40, "type": "multiple_choice", "question": "tomcat安全要求", "options": {"A": "禁止将Tomcat的默认管理界面（如manager, host-manager等）直接暴露,原则上应把这些目录删除或改名", "B": "Tomcat的管理界面默认存在tomcat用户名和密码，必须修改这些默认凭据。", "C": "配置Tomcat的listening参数以禁止目录遍历，确保用户无法直接访问服务器上的文件。", "D": "关闭Tomcat中不需要的服务和功能，如AJP连接器（如果未使用）。"}, "answer": null}, {"id": 41, "type": "multiple_choice", "question": "nginx安全要求", "options": {"A": "必须对Nginx配置文件进行严格的权限管理，确保只有授权用户可以访问和修改。", "B": "必须对Nginx进行安全加固，包括但不限于禁用不必要的HTTP方法、限制请求大小、设置请求头限制等。", "C": "禁止显示详细的错误信息和堆栈跟踪给最终用户。", "D": "禁止开启目录列表功能(autoindex off)"}, "answer": null}, {"id": 42, "type": "multiple_choice", "question": "SpringBoot安全要求", "options": {"A": "禁止在未经身份验证或授权的情况下，直接暴露相关接口路由，如：/v2/api-docs、/swagger-ui.html等。", "B": "禁止使用已知存在安全漏洞的第三方库或框架，应定期检查和更新依赖项。", "C": "原则上禁止启用Springboot监控模块Actuator", "D": "必须对出现的异常进行妥善处理，防止泄露到前端页面，导致因异常处理不当引起安全问题。"}, "answer": null}, {"id": 43, "type": "multiple_choice", "question": "shiro安全要求", "options": {"A": "禁止使用shiro", "B": "禁止使用低版本的shiro，版本应高于1.4.2", "C": "不得使用Shiro的默认密钥、加密算法或凭据进行身份验证和授权。", "D": "确保每个用户或角色只拥有完成其任务所必需的最小权限集合"}, "answer": null}, {"id": 44, "type": "multiple_choice", "question": "struts安全要求", "options": {"A": "Struts已知漏洞较多，原则上禁止使用。", "B": "禁止使用Struts的默认配置、管理账户和密码，必须立即进行更改", "C": "对于Struts管理的用户，必须实施强密码策略，包括长度、复杂度等要求。", "D": "根据最小权限原则，为Struts用户和应用程序分配必要的访问权限。"}, "answer": null}, {"id": 45, "type": "multiple_choice", "question": "RabbitMQ安全要求", "options": {"A": "不得使用RabbitMQ的默认配置、端口、用户名和密码（如guest/guest)，应使用自定义的强密码并修改默认端口", "B": "不得允许未经身份验证或未授权的客户端连接到RabbitMQ服务器。", "C": "不得使用不安全的通信协议（如AMQP over TCP/IP）传输敏感数据，应启用AMQP over SSL/TLS。", "D": "禁止不安全的默认设置：不得使用RabbitMQ的默认设置，如自动创建队列、交换器等，应明确指定和配置。", "E": "对于RabbitMQ的用户和连接，必须实施强密码策略，包括长度、复杂度等要求。", "F": "根据最小权限原则，为RabbitMQ用户和客户端分配必要的访问权限。"}, "answer": null}, {"id": 46, "type": "multiple_choice", "question": "Monitor安全要求", "options": {"A": "Monitor系统不得允许未经身份验证或未授权的访问，包括但不限于API接口、数据库、日志文件等。", "B": "Monitor系统不得使用默认的配置、端口、账户和密码，必须进行自定义配置并更换默认凭据。", "C": "Monitor系统不得直接暴露或允许未经授权的访问到敏感资源，如数据库、配置文件等。", "D": "Monitor系统必须实施严格的访问控制策略，确保只有授权用户能够访问特定资源。"}, "answer": null}, {"id": 47, "type": "multiple_choice", "question": "ueditor安全要求", "options": {"A": "UEditor相关接口及功能不得允许未经身份验证或未授权的用户访问。", "B": "UEditor的文件上传功能必须实施严格的文件类型、大小和内容检查，防止上传恶意文件。", "C": "上传的文件存储在Web服务器可直接访问的目录下", "D": "UEditor不得泄露服务器或应用程序的敏感信息，如版本、路径等。"}, "answer": null}, {"id": 48, "type": "multiple_choice", "question": "log4j安全要求", "options": {"A": "及时更新log4j到最新版本", "B": "禁止在代码中直接使用输入来构建Log4j的配置或日志消息，以防止日志注入攻击", "C": "禁止在Log4j配置中启用JNDI查找", "D": "必须限制对Log4j配置文件的访问权限，确保只有授权的用户或系统可以修改它。"}, "answer": null}, {"id": 49, "type": "multiple_choice", "question": "<PERSON><PERSON><PERSON>安全要求", "options": {"A": "禁止在生产环境中使用Fastjson的autoType特性", "B": "必须对从外部输入获取的JSON字符串进行严格的验证和过滤，防止恶意输入导致的安全漏洞。", "C": "必须使用Fastjson的安全版本，并定期关注官方发布的安全更新和漏洞修复信息。", "D": "必须使用安全模式，可以通过设置ParserConfig的autoTypeAccept为strict来启用。"}, "answer": null}, {"id": 50, "type": "multiple_choice", "question": "不符合mysql安全要求的有", "options": {"A": "只要mysql服务不存在安全漏洞，就不需要关注数据是否加密存储", "B": "在代码中使用root用户方便数据操作", "C": "只要root账户密码够强，就能在任意场景使用root账户", "D": "必须禁用本地读取权限，设置local_infile=off"}, "answer": null}, {"id": 51, "type": "multiple_choice", "question": "redis安全要求", "options": {"A": "禁止使用Redis默认配置的空口令", "B": "禁止使用默认的0.0.0.0监听访问", "C": "必须使用强密码，确保密码的复杂性和难以猜测性。", "D": "数据库不得以root用户身份运行"}, "answer": null}, {"id": 52, "type": "multiple_choice", "question": "操作系统安全要求", "options": {"A": "禁止开启不必要的服务和端口", "B": "必须启用防火墙，限制不必要的访问", "C": "定期检查系统补丁，并及时更新", "D": "禁止安装未授权或来源不明的软件"}, "answer": null}, {"id": 53, "type": "multiple_choice", "question": "审计日志的安全要求", "options": {"A": "系统或者后台必须有日志记录", "B": "必须有日志的查询、统计、分类等功能", "C": "必须对异常事件级别进行划分并提供相应的告警方式", "D": "日志文件只须存储3个月以上"}, "answer": null}, {"id": 54, "type": "multiple_choice", "question": "下列符合用户会话安全要求的选项有", "options": {"A": "必须设置会话超时机制，至少30分钟", "B": "必须限制用户并发会话数", "C": "使用连续的数字作为用户会话标识", "D": "用户登录后服务器应重新下发cookie"}, "answer": null}, {"id": 55, "type": "multiple_choice", "question": "部署过程的安全要求", "options": {"A": "严禁在互联网第三方平台（如阿里云、微信等）部署系统，严禁在github等代码托管平台保存系统源码。", "B": "禁止采用在互联网大区部署代理，应用及数据库均部署在信息内网的架构。", "C": "未经科数部、信通公司批准，严禁擅自将业务系统、设备接入公司网络。", "D": "未经许可，安装、外挂其他组件或应用。"}, "answer": null}, {"id": 56, "type": "multiple_choice", "question": "部署端口管控时需要开启的端口", "options": {"A": "业务端口", "B": "数据端口", "C": "远程管理"}, "answer": null}, {"id": 57, "type": "multiple_choice", "question": "SQL查询安全要求", "options": {"A": "原则上不得通过%、*或者短写关键字进行模糊查询。", "B": "可以将用户输入直接用于SQL查询或命令执行", "C": "可以使用字符拼接的方式构建SQL语句", "D": "对用户输入的一些特殊字符应该进行转义或者删除。"}, "answer": null}, {"id": 58, "type": "multiple_choice", "question": "敏感数据安全要求", "options": {"A": "敏感数据在前端展示时应采用***等方式脱敏", "B": "查看敏感数据，应采用脱敏开关、数字水印等方式进行管控。", "C": "发送给后端时，应采用国密算法进行加密参数传输"}, "answer": null}, {"id": 59, "type": "multiple_choice", "question": "数据传输可以采取那些加密", "options": {"A": "AES加密", "B": "RSA加密", "C": "SM加密", "D": "base64加密"}, "answer": null}, {"id": 60, "type": "multiple_choice", "question": "为防止信息泄露说法正确的是", "options": {"A": "可以在代码中直接硬编码明文敏感信息，", "B": "可以把密钥、加解密函数体保存在前端页面", "C": "不得明文传输用户名、口令等敏感数据。", "D": "禁止把源代码备份打包保存在部署目录下。"}, "answer": null}, {"id": 61, "type": "multiple_choice", "question": "下列说法符合代码安全的是", "options": {"A": "可以使用base64等弱加密函数。", "B": "无需使用的演示系统或历史版本系统禁止部署；", "C": "系统各页面，均应对用户授权进行验证，不得通过url直接访问。", "D": "新版本上线的同时可以不下线老版系统"}, "answer": null}, {"id": 62, "type": "multiple_choice", "question": "符合中间件安全要求有", "options": {"A": "可以不实施输入验证和输出编码，用户输入符合预期格式，对输出的数据可以不进行编码", "B": "必须使用最新的稳定版本，并定期更新以修复已知的安全漏洞。", "C": "必须对中间件系统的日志文件进行定期备份、审查，以便及时发现异常行为和潜在的安全威胁。日志文件应保证有3个月的记录。", "D": "必须对中间件系统进行安全加固，包括但不限于关闭不必要的服务和端口、启用访问控制列表（ACL）等。"}, "answer": null}, {"id": 63, "type": "multiple_choice", "question": "符合中间件安全要求有", "options": {"A": "禁止在中间件系统上执行可能导致系统崩溃或资源耗尽的恶意代码或脚本。", "B": "禁止未经许可的远程访问和管理中间件系统，访问控制应基于最小权限原则。", "C": "禁止不安全的通信，可以使用HTTP协议和HTTPS协议进行敏感数据的传输"}, "answer": null}, {"id": 64, "type": "multiple_choice", "question": "关于Nginx说法正确的是", "options": {"A": "不能在Nginx配置中设置TRACE、PUT、DELETE等HTTP方法", "B": "对Nginx配置文件进行严格的权限管理，确保只有授权用户可以访问和修改。", "C": "对Nginx进行安全加固，包括但不限于限制请求大小、设置请求头限制等", "D": "显示详细的错误信息和堆栈跟踪可以给最终用户"}, "answer": null}, {"id": 65, "type": "multiple_choice", "question": "符合后端的安全要求的是", "options": {"A": "禁止未经许可的远程访问和管理后端系统", "B": "不得使用不安全的协议进行敏感数据的传输，必须启用加密传输。", "C": "可以不使用最新的稳定版本，定期更新以修复已知的安全漏洞即可。", "D": "关闭不必要的服务和端口、启用访问控制列表"}, "answer": null}, {"id": 66, "type": "multiple_choice", "question": "符合Linux说法的是", "options": {"A": "禁止在Linux服务器上安装未经授权或来源不明的软件。", "B": "禁止使用默认密码或弱密码作为系统管理员或任何其他用户账户的密码。", "C": "禁止开启不必要的服务和端口", "D": "必须启用firewall防火墙，只需配置适当的入站规则，限制不必要的网络访问"}, "answer": null}, {"id": 67, "type": "true_false", "question": "从互联网下载、安装与工作无关的软件或非正版软件。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 68, "type": "true_false", "question": "自己重装公司管理信息大区、互联网大区办公终端操作系统。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 69, "type": "true_false", "question": "笔记本电脑接入内网，回家后连接互联网处理工作。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 70, "type": "true_false", "question": "管理信息大区、互联网大区办公终端都必须安装公司专用桌面终端管理系统、防病毒软件。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 71, "type": "true_false", "question": "将管理信息大区办公终端作为无线共享网络接入点为其它网络设备提供接入互联网服务。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 72, "type": "true_false", "question": "办公终端通过插拔网线、配置多网卡、使用无线网卡等方式在管理信息大区网络、互联网大区网络和互联网之间交叉使用或同时连接。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 73, "type": "true_false", "question": "不能私自将公司网络进行改造并组建局域网。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 74, "type": "true_false", "question": "私自在管理信息大区使用对拷线拷贝、存储内网数据。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 75, "type": "true_false", "question": "为他人实施危害网络安全的活动提供技术支持。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 76, "type": "true_false", "question": "未备案情况下，利用公司网络进行网络攻击。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 77, "type": "true_false", "question": "未备案情况下，浏览访问“暗网”、使用“翻墙软件”。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 78, "type": "true_false", "question": "禁止私自发布涉及公司的微信小程序、公众号。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 79, "type": "true_false", "question": "在运、在建的信息系统可以对外开放不必要的管理端口、业务访问端口、数据传输端口。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 80, "type": "true_false", "question": "在建的信息系统或应用首页可以不登录直接打开。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 81, "type": "true_false", "question": "使用非安全移动存储介质存储、处理敏感信息。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 82, "type": "true_false", "question": "严格保密研发、运维等工作中收集的用户信息、公司数据及文档等。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 83, "type": "true_false", "question": "可以使用未脱敏生产数据进行研发、测试等。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 84, "type": "true_false", "question": "不得利用电力公司信息网络从事危害国家安全、泄露国家秘密等违法犯罪活动，不得制作、查阅、复制和传播妨碍社会治安和淫秽色情等有害信息。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 85, "type": "true_false", "question": "未经许可，可以将在工作中获取的国网湖南省电力有限公司网络与信息系统帐号和口令泄漏给第三方。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 86, "type": "true_false", "question": "需穿透互联网大区、信息内网的项目，可以采用在互联网大区部署代理的方式。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 87, "type": "true_false", "question": "为更好的满足用户全局搜索需求，可设置%、*或者短写关键字进行模糊查询。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 88, "type": "true_false", "question": "应用程序在设置用户标识时可以重复。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 89, "type": "true_false", "question": "应用程序可以遗留测试代码和目录。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 90, "type": "true_false", "question": "禁止在应用程序代码中有存有地址等信息的泄露。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 91, "type": "true_false", "question": "文件的上传下载可以不进行限制。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 92, "type": "true_false", "question": "必须对数据库配置文件的连接数据库的密码加密。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 93, "type": "true_false", "question": "审计数据产生系统或者后台必须有日志记录。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 94, "type": "true_false", "question": "可以不对异常事件级别进行划分并提供相应的告警方式。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 95, "type": "true_false", "question": "部署端口管控时可以开启80等业务端口。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 96, "type": "true_false", "question": "部署端口管控时不能开启远程端口。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 97, "type": "true_false", "question": "可以不对用户输入进行验证。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 98, "type": "true_false", "question": "个人信息、负面清单等敏感数据应当通过相应的算法进行加密保存。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 99, "type": "true_false", "question": "进行数据传输必须进行加密。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 100, "type": "true_false", "question": "进行敏感数据查询操作时，参数传输可以采用GET方法传递。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 101, "type": "true_false", "question": "无登录页面应用禁止部署。", "options": {"A": "正确", "B": "错误"}, "answer": null}, {"id": 102, "type": "true_false", "question": "敏感的数据可以使用base64等弱加密函数。", "options": {"A": "正确", "B": "错误"}, "answer": null}], "answer_validation": {"single_choice": {"type": "string", "valid_values": ["A", "B", "C", "D", "E", "F"], "description": "单选题答案为单个选项字符"}, "multiple_choice": {"type": "array", "item_type": "string", "valid_values": ["A", "B", "C", "D", "E", "F"], "description": "多选题答案为选项字符数组"}, "true_false": {"type": "boolean", "valid_values": [true, false], "description": "判断题答案为布尔值"}}}